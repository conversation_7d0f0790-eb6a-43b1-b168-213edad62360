<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
        ]);

        // Create coaches
        User::create([
            'name' => 'Coach <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'coach',
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'coach',
        ]);

        // Create players
        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'player',
        ]);

        User::create([
            'name' => '<PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'player',
        ]);

        User::create([
            'name' => 'Sam Taylor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'player',
        ]);

        User::create([
            'name' => 'Jordan Brown',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'player',
        ]);
    }
}
