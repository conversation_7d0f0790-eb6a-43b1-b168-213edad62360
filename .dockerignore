# Git
.git
.gitignore
.gitattributes

# Docker
Dockerfile
docker-compose.yml
.dockerignore
DOCKER_README.md

# Environment files
.env
.env.local
.env.production
.env.backup

# Dependencies
vendor/
node_modules/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Laravel
/storage/*.key
/storage/logs/*
/storage/framework/cache/*
/storage/framework/sessions/*
/storage/framework/views/*
/bootstrap/cache/*

# Testing
.phpunit.result.cache
/coverage

# Build artifacts
/public/build
/public/hot
/public/storage

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
