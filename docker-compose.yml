version: '3.8'

services:
  # Laravel Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: football_scout_app
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - .:/var/www/html
      - ./storage:/var/www/html/storage
    environment:
      - APP_ENV=production
      - APP_DEBUG=false
      - DB_CONNECTION=mysql
      - DB_HOST=mysql
      - DB_PORT=3306
      - DB_DATABASE=football_scout
      - DB_USERNAME=root
      - DB_PASSWORD=rootpassword
    depends_on:
      - mysql
    networks:
      - football_network

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: football_scout_mysql
    restart: unless-stopped
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: football_scout
      MYSQL_USER: football_user
      MYSQL_PASSWORD: football_password
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - football_network

  # phpMyAdmin
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: football_scout_phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: rootpassword
      MYSQL_ROOT_PASSWORD: rootpassword
    depends_on:
      - mysql
    networks:
      - football_network

  # Redis (optional, for caching)
  redis:
    image: redis:7-alpine
    container_name: football_scout_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - football_network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local

networks:
  football_network:
    driver: bridge
